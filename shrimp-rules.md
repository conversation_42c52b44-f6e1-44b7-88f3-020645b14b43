# Qt桌面应用迁移项目开发规范

## 项目概述

**项目目标**: 将现有的图像/视频批量处理系统从前后端分离架构（FastAPI + Web前端）完全迁移到Qt桌面应用
**技术栈**: PySide6 + SQLite + 本地文件系统 + 复用现有Python处理逻辑
**核心功能**: 17种图像/视频处理操作，GB级大文件处理，批量处理，实时进度反馈

## 项目架构

### 目录结构规范

```
qt_clover/                          # Qt桌面应用根目录
├── main.py                         # 应用入口
├── ui/                            # 视图层
│   ├── main_window.py             # 主窗口
│   ├── task_manager_widget.py     # 任务管理组件
│   ├── progress_monitor.py        # 进度监控组件
│   ├── result_viewer.py           # 结果查看组件
│   └── settings_dialog.py         # 设置对话框
├── controllers/                   # 控制器层
│   ├── task_controller.py         # 任务控制器
│   ├── file_controller.py         # 文件控制器
│   └── progress_controller.py     # 进度控制器
├── core/                          # 核心业务层（复用现有）
│   ├── processors/                # 处理器（直接复用）
│   ├── models/                    # 数据模型（适配SQLite）
│   ├── services/                  # 服务层（适配本地）
│   └── database.py               # SQLite数据库
├── workers/                       # 工作线程
│   ├── task_worker.py            # 任务执行线程
│   ├── progress_worker.py        # 进度监控线程
│   └── file_worker.py            # 文件处理线程
└── utils/                         # 工具模块
    ├── local_file_manager.py     # 本地文件管理器
    ├── thread_manager.py         # 线程管理器
    └── signal_manager.py         # 信号管理器
```

## 代码复用规则

### 必须直接复用的模块（禁止修改核心逻辑）

- **backend_v2/app/services/image_processor_base.py** → core/processors/image_processor_base.py
- **backend_v2/app/services/image_processors/** → core/processors/image_processors/
- **backend_v2/app/services/video_processor_base.py** → core/processors/video_processor_base.py
- **backend_v2/app/services/video_processors/** → core/processors/video_processors/
- **backend_v2/app/services/task_management.py** → core/services/task_management.py
- **backend_v2/app/services/task_state_transition.py** → core/services/task_state_transition.py
- **backend_v2/app/services/file_metadata_service.py** → core/services/file_metadata_service.py

### 需要适配的模块（保持接口一致）

- **backend_v2/app/models/** → core/models/ (适配SQLite)
- **backend_v2/app/crud/** → core/crud/ (适配SQLite)
- **backend_v2/app/services/minio_service.py** → utils/local_file_manager.py (接口兼容)

## 技术适配规则

### 数据库适配规则

- **JSONB字段适配**: 将PostgreSQL的JSONB字段改为SQLite的Text类型，存储JSON字符串
- **保持ORM映射**: 继续使用SQLAlchemy ORM，只修改字段类型定义
- **保持数据结构**: Task/Batch/File/Result四层数据模型结构不变
- **状态枚举保持**: 所有TaskStatus、BatchStatus等枚举值保持不变

### 文件管理适配规则

- **本地目录结构**: ~/clover_data/{raw,result,thumb,temp}/
- **接口兼容性**: LocalFileManager必须实现与MinIOService相同的方法签名
- **路径转换**: 所有MinIO URL格式必须转换为本地文件路径
- **元数据保持**: 文件校验和、重复检测等功能必须保持

### 并发处理适配规则

- **QThread替代**: 用QThread替代Celery Worker，保持相同的任务执行逻辑
- **队列分类保持**: 继续使用CPU/GPU/IO三种队列分类
- **信号槽机制**: 用Qt信号槽替代Celery信号进行进度反馈
- **线程安全**: 确保UI操作在主线程，处理操作在工作线程

## 开发流程规则

### 任务执行顺序（严格按依赖关系）

1. **项目基础架构搭建** → 2. **数据模型和数据库层迁移** → 3. **本地文件管理系统实现**
4. **图像处理引擎迁移** → 5. **视频处理引擎迁移** → 6. **任务管理和状态控制系统**
7. **主界面和任务提交功能** → 8. **任务监控和进度显示** → 9. **结果管理和文件操作**
10. **系统设置和配置管理** → 11. **集成测试和性能优化** → 12. **打包部署和文档编写**

### 验收标准规则

- **功能完整性**: 所有17种处理操作必须正常工作
- **性能标准**: 处理速度不低于原系统80%
- **界面响应**: UI响应时间 < 100ms
- **大文件支持**: 支持GB级文件稳定处理
- **错误处理**: 完善的异常处理和错误恢复机制

## 代码标准

### 命名约定

- **文件命名**: 使用snake_case，如task_manager.py
- **类命名**: 使用PascalCase，如TaskManager
- **方法命名**: 使用snake_case，如process_image()
- **常量命名**: 使用UPPER_CASE，如MAX_FILE_SIZE

### Qt特定规则

- **信号定义**: 使用Signal()定义信号，如progress_updated = Signal(int)
- **槽函数命名**: 使用on_前缀，如on_task_completed()
- **UI更新**: 所有UI更新必须在主线程中执行
- **资源管理**: 及时释放QThread和文件句柄

## 禁止操作

### 严格禁止

- **禁止修改处理器核心算法**: ImageProcessorBase及其子类的process_image()方法
- **禁止改变数据模型结构**: Task/Batch/File/Result的字段定义和关系
- **禁止在UI线程执行耗时操作**: 图像/视频处理必须在工作线程
- **禁止重写已验证的业务逻辑**: TaskStateTransitionService的状态转换规则

### 必须遵循

- **必须保持接口一致性**: 替换组件必须实现相同的方法签名
- **必须使用Qt信号槽**: 组件间通信和进度反馈
- **必须进行错误处理**: 每个处理步骤都要有异常捕获
- **必须记录操作日志**: 使用structlog记录关键操作

## 关键文件交互规则

### 同时修改要求

- **修改数据模型时**: 必须同时更新对应的CRUD操作和Schema定义
- **修改处理器时**: 必须同时更新工厂方法和注册机制
- **修改UI组件时**: 必须同时更新对应的控制器和信号连接
- **修改配置时**: 必须同时更新默认值和验证规则

### 依赖检查要求

- **修改core/models/前**: 必须确保core/database.py已正确配置
- **修改processors/前**: 必须确保utils/local_file_manager.py已实现
- **修改UI组件前**: 必须确保对应的控制器和业务逻辑已完成
- **修改workers/前**: 必须确保core/services/已正确迁移

## AI决策标准

### 优先级判断

1. **功能完整性** > 性能优化 > 界面美观
2. **代码复用** > 重新实现
3. **架构一致性** > 局部优化
4. **错误处理** > 功能扩展

### 冲突解决

- **接口冲突**: 优先保持与现有业务逻辑的兼容性
- **性能冲突**: 优先保证功能正确性，后续优化性能
- **技术选择**: 优先选择与现有技术栈兼容的方案
- **实现方式**: 优先选择风险最低的实现方式
